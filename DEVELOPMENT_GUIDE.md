# OLX Clone - Development Guide for Mac M4

## Quick Start Commands

```bash
# Start development server
npm start

# Run tests
npm test

# Build for production
npm run build

# Install dependencies (if needed again)
npm install --legacy-peer-deps
```

## Application URLs

- **Development:** http://localhost:3001
- **Production Build:** Run `npm run build` then serve the `build` folder

## Project Structure

```
src/
├── Components/          # Reusable UI components
├── Pages/              # Main page components
├── Context/            # React Context providers
├── Redux/              # Redux store and actions
├── Routes/             # Routing configuration
├── Data/               # Static data and constants
└── theme/              # Chakra UI theme customization
```

## Key Technologies

- **React 18.2.0** - Frontend framework
- **Chakra UI** - Component library
- **Redux + Redux Thunk** - State management
- **React Router v6** - Client-side routing
- **Axios** - HTTP client
- **Framer Motion** - Animations

## API Integration

The app connects to: `https://olx-database-3xly.onrender.com`

### Main Endpoints:
- `/allOlxData` - Product listings
- `/homedata` - Homepage data
- `/data` - User data
- `/data/{id}` - Specific user data

## Development Tips

### 1. Hot Reload
Changes to source files automatically reload the browser.

### 2. ESLint Warnings
The app has some ESLint warnings but they don't affect functionality.
To fix them gradually, run: `npm run lint`

### 3. Browser Compatibility
Optimized for modern browsers. Supports:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

### 4. Mobile Testing
The app is responsive. Test on different screen sizes using browser dev tools.

## Troubleshooting

### Port Already in Use
If port 3001 is busy, React will automatically suggest another port.

### Dependency Issues
If you encounter dependency conflicts:
```bash
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

### Build Issues
For production builds:
```bash
npm run build
# Serve the build folder with any static server
npx serve -s build
```

## Testing the Application

### Manual Testing Checklist:
- [ ] Home page loads with product listings
- [ ] Navigation works (Cars, Mobile, Books)
- [ ] Search functionality
- [ ] Login/signup flow
- [ ] Product detail pages
- [ ] User profile pages
- [ ] Responsive design on mobile

### Automated Testing:
```bash
npm test
```

## Performance Optimization

### For Development:
- Use React Developer Tools browser extension
- Monitor network requests in browser dev tools
- Check console for any errors

### For Production:
- Run `npm run build` for optimized bundle
- Use browser caching for static assets
- Consider implementing service workers for offline functionality

## Next Steps for Development

1. **Add Environment Variables** - Create `.env` file for API URLs
2. **Implement Error Boundaries** - Better error handling
3. **Add Unit Tests** - Increase test coverage
4. **Optimize Bundle Size** - Code splitting and lazy loading
5. **Add PWA Features** - Service workers, offline support
6. **Implement Real Authentication** - JWT tokens, secure login
7. **Add Image Optimization** - Lazy loading, WebP format
8. **Database Integration** - Replace external API with local database

## Useful Browser Extensions

- **React Developer Tools** - Debug React components
- **Redux DevTools** - Debug Redux state
- **Lighthouse** - Performance auditing
- **Web Vitals** - Core web vitals monitoring
