@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");

.box {
  display: flex;
  align-items: center;
  /* border: 1px solid #000; */
  height: 40px;
}

.container {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 10px;
  font-family: "Roboto", sans-serif;
  cursor: pointer;
  font-size: 15px;
  color: #002f34;
  font-weight: 500;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: -2px;
  left: 3px;
  height: 25px;
  width: 25px;
  background-color: #fff;
  border: 2px solid #ced7d8;
  /* border: 8px solid #000; */
}

.container:hover input ~ .checkmark {
  background-color: #fff;
  border: 2px solid black;
}
.container input:checked ~ .checkmark {
  background-color: black;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.container input:checked ~ .checkmark:after {
  display: block;
}

.container .checkmark:after {
  left: 7px;
  top: 0px;
  width: 8px;
  height: 16px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
