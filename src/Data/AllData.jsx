// import { useSelector } from "react-redux";

export const AllBrands = [
  {
    id: 1,
    brand: "Maruti Suzuki",
  },
  {
    id: 2,
    brand: "Hyundai",
  },
  {
    id: 3,
    brand: "Mahindra",
  },
  {
    id: 4,
    brand: "Tata",
  },
  {
    id: 5,
    brand: "Honda",
  },
  {
    id: 6,
    brand: "Ford",
  },
  {
    id: 7,
    brand: "Toyota",
  },
  {
    id: 8,
    brand: "Chevrolet",
  },
  {
    id: 9,
    brand: "Renualt",
  },
  {
    id: 10,
    brand: "Skoda",
  },
];
export const AllCarModel = [
  {
    id: 1,
    brand: "Skoda Fabia",
  },
  {
    id: 2,
    brand: "Skoda Rapid",
  },
  {
    id: 3,
    brand: "Renualt Duster",
  },
  {
    id: 4,
    brand: "Maruti Suzuki Baleno",
  },
  {
    id: 5,
    brand: "Hyundai Verna",
  },
  {
    id: 6,
    brand: "Mahindra Scorpio",
  },
  {
    id: 7,
    brand: "Hyundai Creta",
  },
  {
    id: 8,
    brand: "Mahindra KUV 100",
  },
  {
    id: 9,
    brand: "Hyundai Alcazar",
  },
  {
    id: 10,
    brand: "Maruti Suzuki Vitara Brezza",
  },
  {
    id: 11,
    brand: "Maruti Suzuki Ciaz",
  },
  {
    id: 12,
    brand: "Maruti Suzuki Swift Dezire",
  },
  {
    id: 13,
    brand: "Maruti Suzuki Swift",
  },
  {
    id: 14,
    brand: "Tata Nexon",
  },
  {
    id: 15,
    brand: "Tata Tigore",
  },
  {
    id: 16,
    brand: "Tata Vista",
  },
  {
    id: 17,
    brand: "Honda Amaze",
  },
  {
    id: 18,
    brand: "Honade City",
  },
  {
    id: 19,
    brand: "Honda Accord",
  },
];
export const familyTree = {
  //Grandfather
  name: "India",
  items: 90,
  children: [
    {
      name: "Madhya Pradesh",
      items: 60,
      children: [
        {
          name: "Indore",
          items: 35,
        },
        {
          name: "Bhopal",
          items: 60,
        },
        {
          name: "Gwalior",
          items: 57,
        },
        {
          name: "Jabalpur",
          items: 57,
        },
        {
          name: "Ujjain",
          items: 57,
        },
      ],
    },
    {
      name: "Maharashtra",
      items: 60,
      children: [
        {
          name: "Mumbai",
          items: 35,
        },
        {
          name: "Pune",
          items: 60,
        },
        {
          name: "Thane",
          items: 57,
        },
        {
          name: "Nashik",
          items: 57,
        },
        {
          name: "Kolhapur",
          items: 57,
        },
      ],
    },
    {
      name: "Uttar Pradesh",
      items: 55,
      children: [
        {
          name: "Lucknow",
          items: 35,
        },
        {
          name: "Ghaziabad",
          items: 60,
        },
        {
          name: "Kanpur",
          items: 57,
        },
        {
          name: "Varanashi",
          items: 57,
        },
        {
          name: "Noida",
          items: 57,
        },
      ],
    },
  ],
};
export const carCategory = {
  name: "All Cars",
  items: 90,
  children: [
    {
      name: "Cars",
      items: 60,
    },
  ],
};

export const mobileBrands = [
  {
    id: 1,
    brand: "Google Pixel 6A.",
  },
  {
    id: 2,
    brand: "OnePlus 9.",
  },
  {
    id: 3,
    brand: "Apple iPhone 13.",
  },
  {
    id: 4,
    brand: "Samsung Galaxy F62",
  },
  {
    id: 5,
    brand: "Realme Narzo 30A",
  },
  {
    id: 6,
    brand: "Vivo V20",
  },
  {
    id: 7,
    brand: "OPPO A53",
  },
  {
    id: 8,
    brand: "Xiaomi Redmi Note 10 Pro",
  },
  {
    id: 9,
    brand: "iPhone SE 2022",
  },
  {
    id: 10,
    brand: "iphone 14",
  },
];

export const mobileCategory = [
  {
    name: "All Mobile",
    items: 90,
    children: [
      {
        name: "Mobile",
        items: 60,
      },
    ],
  },
];
