// import {extendTheme, withDefaultColorScheme, withDefaultVariant} from "@chakra-ui/react"
// import { mode } from "@chakra-ui/theme-tools";

// const theme = extendTheme({

//     colors:{
//        main:'#000'
//     },

//     Component:{
//          Input:{
//             borderRadius:'none',
           

           
           
//          }
//     },



   

   
// }
// ,
// withDefaultColorScheme({
//     colorScheme:"def"
// }),
// withDefaultVariant({
//     variant:"fields",
   
// })
   
     
   

  
// )

// export default theme;

