{"name": "olx-clone-react", "version": "0.1.0", "private": true, "dependencies": {"@chakra-ui/icons": "^2.0.11", "@chakra-ui/react": "^2.3.6", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.1.3", "framer-motion": "^7.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.6.0", "react-redux": "^8.0.4", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.4.2", "react-scripts": "5.0.1", "react-scroll-to-top": "^3.0.0", "redux": "^4.2.0", "redux-thunk": "^2.4.1", "styled-components": "^5.3.6", "swiper": "^11.2.10", "uuid": "^9.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "."}