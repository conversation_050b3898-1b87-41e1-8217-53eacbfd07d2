<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link
      rel="icon"
      href="data:image/png;base64,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"
    />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />

    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>
      OLX-Free classifieds in india, Buy and Sell for free anywhere in india...
    </title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
